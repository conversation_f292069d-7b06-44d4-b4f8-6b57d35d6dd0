package com.maguo.loan.cash.flow.entrance.common.service;

import com.maguo.loan.cash.flow.entity.common.ProjectElements;
import com.maguo.loan.cash.flow.entity.common.ProjectElementsExt;
import com.maguo.loan.cash.flow.entity.common.ProjectContractCapital;
import com.maguo.loan.cash.flow.entity.common.ProjectContractFlow;
import com.maguo.loan.cash.flow.entity.vo.ProjectInfoVO;
import com.maguo.loan.cash.flow.repository.ProjectInfoRepository;
import com.maguo.loan.cash.flow.repository.ProjectElementsRepository;
import com.maguo.loan.cash.flow.repository.ProjectElementsExtRepository;
import com.maguo.loan.cash.flow.repository.ProjectContractCapitalRepository;
import com.maguo.loan.cash.flow.repository.ProjectContractFlowRepository;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 项目相关服务
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 14:11
 */
@Service
public class ProjectInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoService.class);

    @Autowired
    private ProjectInfoRepository projectInfoRepository;

    @Autowired
    private ProjectElementsRepository projectElementsRepository;

    @Autowired
    private ProjectElementsExtRepository projectElementsExtRepository;

    @Autowired
    private ProjectContractCapitalRepository projectContractCapitalRepository;

    @Autowired
    private ProjectContractFlowRepository projectContractFlowRepository;

    /**
     * 根据项目编码查询项目完整信息
     *
     * @param projectCode 项目编码
     * @return 项目完整信息VO
     */
    public ProjectInfoVO queryProjectInfo(String projectCode) {
        logger.info("开始查询项目完整信息，projectCode: {}", projectCode);

        try {
            // 1. 查询项目基本信息
            ProjectInfoVO vo = projectInfoRepository.findByProjectCode(projectCode);
            if (vo == null) {
                logger.info("未找到项目信息，projectCode: {}", projectCode);
                return null;
            }

            // 2. 查询项目要素
            ProjectElements elements = projectElementsRepository.findByProjectCode(projectCode);
            if (elements != null) {
                vo.setElements(elements);
                logger.info("查询到项目要素信息，projectCode: {}", projectCode);
            } else {
                logger.info("未找到项目要素信息，projectCode: {}", projectCode);
            }

            // 3. 查询项目要素扩展
            ProjectElementsExt elementsExt = projectElementsExtRepository.findByProjectCode(projectCode);
            if (elementsExt != null) {
                vo.setElementsExt(elementsExt);
                logger.info("查询到项目要素扩展信息，projectCode: {}", projectCode);
            } else {
                logger.info("未找到项目要素扩展信息，projectCode: {}", projectCode);
            }

            // 4. 查询项目资金合同
            ProjectContractCapital contractCapital = projectContractCapitalRepository.findByProjectCode(projectCode);
            if (ObjectUtils.isNotEmpty(contractCapital)) {
                vo.setContractCapital(contractCapital);
                logger.info("查询到项目资金合同信息, projectCode: {}", projectCode);
            } else {
                logger.info("未找到项目资金合同信息，projectCode: {}", projectCode);
            }

            // 5. 查询项目资产合同
            ProjectContractFlow contractFlow = projectContractFlowRepository.findByProjectCode(projectCode);
            if (ObjectUtils.isNotEmpty(contractFlow)) {
                vo.setContractFlow(contractFlow);
                logger.info("查询到项目资产合同信息, projectCode: {}", projectCode);
            } else {
                logger.info("未找到项目资产合同信息，projectCode: {}", projectCode);
            }

            logger.info("项目完整信息查询完成，projectCode: {}", projectCode);
            return vo;

        } catch (Exception e) {
            logger.error("查询项目完整信息异常，projectCode: {}", projectCode, e);
            throw new RuntimeException("查询项目信息失败", e);
        }
    }


}
